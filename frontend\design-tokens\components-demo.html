<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Components Demo - Material 3 重構版</title>
    <link rel="stylesheet" href="components.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            gap: 2rem;
        }

        .demo-section {
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--md-sys-color-outline);
            background-color: var(--md-sys-color-surface-container);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .component-showcase {
            padding: 1rem;
            border-radius: 8px;
            background-color: var(--md-sys-color-surface);
            border: 1px solid var(--md-sys-color-outline);
        }
    </style>
</head>
<body class="light-theme">
    <!-- 主題切換按鈕 -->
    <button class="theme-toggle-btn" onclick="toggleTheme()" title="切換主題">
        🌙
    </button>

    <div class="demo-container">
        <header>
            <h1 class="md-headline-large" style="color: var(--md-sys-color-primary); margin-bottom: 0.5rem;">
                Components Demo
            </h1>
            <p class="md-body-large text-secondary">
                重構後的組件展示 - 基於 Material 3 Design Tokens
            </p>
        </header>

        <!-- 按鈕展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">按鈕組件</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">主要按鈕</h3>
                    <button class="btn btn-primary">Primary Button</button>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">次要按鈕</h3>
                    <button class="btn btn-secondary">Secondary Button</button>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">按鈕尺寸</h3>
                    <button class="btn btn-primary btn-sm">Small</button>
                    <button class="btn btn-primary">Medium</button>
                    <button class="btn btn-primary btn-lg">Large</button>
                </div>
            </div>
        </section>

        <!-- 表單組件展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">表單組件</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">輸入框</h3>
                    <input type="text" class="input" placeholder="請輸入文字...">
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">表單控制項</h3>
                    <select class="form-control">
                        <option>選項 1</option>
                        <option>選項 2</option>
                        <option>選項 3</option>
                    </select>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">搜尋選擇</h3>
                    <div class="search-input-container">
                        <span class="search-icon">🔍</span>
                        <select class="form-control search-select">
                            <option>搜尋選項...</option>
                            <option>中文</option>
                            <option>English</option>
                            <option>日本語</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- 語言標籤展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">語言標籤</h2>
            <div class="selected-languages">
                <span class="language-pill">
                    中文
                    <button class="remove-pill" type="button">&times;</button>
                </span>
                <span class="language-pill">
                    English
                    <button class="remove-pill" type="button">&times;</button>
                </span>
                <span class="language-pill">
                    日本語
                    <button class="remove-pill" type="button">&times;</button>
                </span>
            </div>
        </section>

        <!-- 卡片展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">卡片組件</h2>
            <div class="demo-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">卡片標題</h3>
                    </div>
                    <div class="card-content">
                        這是卡片內容，展示了重構後的樣式效果。
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">另一個卡片</h3>
                    </div>
                    <div class="card-content">
                        支援主題切換的卡片組件。
                    </div>
                </div>
            </div>
        </section>

        <!-- 訊息展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">狀態訊息</h2>
            <div style="display: grid; gap: 1rem;">
                <div class="message message-success">
                    ✅ 成功訊息 - 操作已完成
                </div>
                <div class="message message-warning">
                    ⚠️ 警告訊息 - 請注意相關事項
                </div>
                <div class="message message-error">
                    ❌ 錯誤訊息 - 發生了錯誤
                </div>
                <div class="message message-info">
                    ℹ️ 資訊訊息 - 提供額外資訊
                </div>
            </div>
        </section>

        <!-- 工具類別展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">工具類別</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">文字顏色</h3>
                    <p class="text-primary">主要文字顏色</p>
                    <p class="text-secondary">次要文字顏色</p>
                    <p class="text-tertiary">第三級文字顏色</p>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">字重</h3>
                    <p class="font-light">輕字重</p>
                    <p class="font-normal">標準字重</p>
                    <p class="font-medium">中等字重</p>
                    <p class="font-bold">粗字重</p>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">連結</h3>
                    <a href="#" class="link">這是一個連結</a>
                </div>
            </div>
        </section>

        <!-- 使用說明 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">重構特色</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">🎨 主題切換</h3>
                    <p class="md-body-medium">點擊右上角按鈕切換明暗主題</p>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">📱 響應式</h3>
                    <p class="md-body-medium">字體大小自動適應螢幕尺寸</p>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">🔧 Material 3</h3>
                    <p class="md-body-medium">基於 Material 3 Design Tokens</p>
                </div>
            </div>
        </section>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const btn = document.querySelector('.theme-toggle-btn');
            
            if (body.classList.contains('light-theme')) {
                body.classList.remove('light-theme');
                body.classList.add('dark-theme');
                btn.textContent = '☀️';
                btn.title = '切換到明亮主題';
            } else {
                body.classList.remove('dark-theme');
                body.classList.add('light-theme');
                btn.textContent = '🌙';
                btn.title = '切換到深色主題';
            }
        }

        // 移除按鈕功能
        document.querySelectorAll('.remove-pill').forEach(btn => {
            btn.addEventListener('click', function() {
                this.parentElement.remove();
            });
        });
    </script>
</body>
</html>
