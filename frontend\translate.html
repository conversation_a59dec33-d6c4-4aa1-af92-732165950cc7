<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>STT-SaaS</title>
    <!-- 主要樣式檔案 (已包含所有設計權杖) -->
    <link rel="stylesheet" href="design-tokens/components.css">
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@100;300;400;500;600;700;800;900&display=swap">
    <!-- Material Symbols 圖示 -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
</head>

<body>
    <main class="bg-primary translate-main">
        <!-- 1. 頂部控制按鈕區 -->
        <section class="control-buttons">
            <button class="btn btn-secondary language-settings-btn">
                <span class="material-symbols-outlined">translate</span>
                語言設定
            </button>
            <header class="app-header">
                <h1>即時翻譯</h1>
            </header>
            <button class="btn btn-secondary leave-meeting-btn">
                <span class="material-symbols-outlined">logout</span>
                離開會議
            </button>
        </section>
        <!-- 2. 已選擇語言標籤區 -->
        <section class="tool-bar">
            <!-- 水平排列的語言標籤 -->
            <div class="language-tags">
                <span class="btn btn-primary language-tag">English</span>
                <span class="btn btn-primary language-tag">日本語</span>
                <span class="btn btn-primary language-tag">繁體中文</span>
                <span class="btn btn-primary language-tag">한국어</span>
            </div>
        </section>

        <!-- 3. 翻譯語言顯示區域 -->
        <section class="translation-display-area">
            <div class="translation-flex">
                <div class="translation-column" data-lang="en">
                    <div class="translation-content">
                        <p>During the meeting, please refrain from moving around or engaging in conversation.</p>
                        <p>Thank you for your cooperation. Today's session is expected to last until around 4 PM.</p>
                        <p><strong>If you have any questions, feel free to ask the staff nearby.</strong></p>
                    </div>
                </div>
                <div class="translation-column" data-lang="ja">
                    <div class="translation-content">
                        <p>会議中は席を立ったり、私語を控えていただきますようお願いいたします。</p>
                        <p>ご協力ありがとうございます。本日の会議は午後4時頃まで予定しております。</p>
                        <p><strong>ご不明な点がございましたら、近くのスタッフにお尋ねください。</strong></p>
                    </div>
                </div>
                <div class="translation-column" data-lang="zh">
                    <div class="translation-content">
                        <p>會議期間請勿隨意走動，請務必保持安靜。</p>
                        <p>會議期間請勿隨意走動或交談，感謝配合。今天的會議預計到四點左右。</p>
                        <p><strong>隨時有問題都可以向身旁的工作人員詢問。</strong></p>
                    </div>
                </div>
                <div class="translation-column" data-lang="ko">
                    <div class="translation-content">
                        <p>회의에 주시기 바라며 대화를 삼가 주시기 바랍니다.</p>
                        <p>협조해 주셔서 감사합니다. 오늘 회의는 오후 4시쯤까지 진행될 예정입니다.</p>
                        <p><strong>궁금한 점이 있으시면 가까운 스태프에게 언제든지 문의해 주세요.</strong></p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 4. 即時字幕區域 -->
        <section class="live-subtitles">
            <h2 class="subtitle-title">即時字幕</h2>
            <div class="subtitle-content">
                <p>各位貴賓，歡迎蒞臨本次國際會議。為了確保會議順利進行，請務必保持手機靜音或震動模式。會議期間請勿隨意走動或交談，感謝配合。今天的會議預計到四點左右，隨時有問題都可以向身旁的工作人員詢問。</p>
            </div>
        </section>

        <!-- 5. QR Code浮動按鈕 -->
        <button class="qr-code-btn">
            <span class="material-symbols-outlined">qr_code_scanner</span>
        </button>
        <!-- 6. QR Code 掃描頁面 -->
        <!-- QR Code Card 模態框 -->
        <div class="qr-code-card-overlay hidden" id="qrCodeCardOverlay" role="dialog" aria-modal="true" aria-labelledby="qr-card-title">
            <div class="qr-code-card" role="document">
                <!-- 卡片標題區域 -->
                <header class="qr-card-header">
                    <h2 class="qr-card-title" id="qr-card-title">會議連結</h2>
                    <button class="qr-card-close-btn" aria-label="關閉會議連結視窗" type="button">
                        <span class="material-symbols-outlined" aria-hidden="true">close</span>
                    </button>
                </header>

                <!-- QR Code 內容區域 -->
                <div class="qr-card-content">
                    <!-- QR Code 圖片容器 -->
                    <div class="qr-code-container">
                        <div class="qr-code-image" role="img" aria-label="會議連結 QR Code">
                            <!-- QR Code 圖片將通過 CSS 背景圖片或 JavaScript 動態生成 -->
                            <div class="qr-code-placeholder">
                                <!-- 這裡可以放置實際的 QR Code 圖片或 SVG -->
                                <span class="material-symbols-outlined qr-placeholder-icon" aria-hidden="true">qr_code</span>
                            </div>
                        </div>
                    </div>

                    <!-- SCAN ME 按鈕 -->
                    <div class="scan-button-container">
                        <button class="scan-me-btn" type="button" aria-label="掃描 QR Code 以加入會議">
                            <span class="material-symbols-outlined scan-icon" aria-hidden="true">smartphone</span>
                            <span class="scan-text">SCAN ME</span>
                        </button>
                    </div>

                    <!-- 網址顯示區域 -->
                    <div class="url-display-container">
                        <div class="url-display" role="textbox" aria-readonly="true" aria-label="會議連結網址">
                            <span class="url-text">https://www.fenrirdata.com</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 基本互動功能
        document.addEventListener('DOMContentLoaded', function () {
            // QR Code 按鈕點擊效果
            const qrBtn = document.querySelector('.qr-code-btn');
            const qrCardOverlay = document.getElementById('qrCodeCardOverlay');
            const qrCardCloseBtn = document.querySelector('.qr-card-close-btn');

            // 顯示 QR Code 卡片
            qrBtn.addEventListener('click', function () {
                qrCardOverlay.classList.remove('hidden');
                qrCardOverlay.setAttribute('aria-hidden', 'false');
                // 聚焦到關閉按鈕以提升無障礙體驗
                qrCardCloseBtn.focus();
                // 防止背景滾動
                document.body.style.overflow = 'hidden';
            });

            // 關閉 QR Code 卡片
            function closeQrCard() {
                qrCardOverlay.classList.add('hidden');
                qrCardOverlay.setAttribute('aria-hidden', 'true');
                // 恢復背景滾動
                document.body.style.overflow = '';
                // 將焦點返回到 QR Code 按鈕
                qrBtn.focus();
            }

            // 關閉按鈕點擊事件
            qrCardCloseBtn.addEventListener('click', closeQrCard);

            // 點擊遮罩關閉卡片
            qrCardOverlay.addEventListener('click', function (e) {
                if (e.target === qrCardOverlay) {
                    closeQrCard();
                }
            });

            // ESC 鍵關閉卡片
            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape' && !qrCardOverlay.classList.contains('hidden')) {
                    closeQrCard();
                }
            });

            // 語言設定按鈕
            const langBtn = document.querySelector('.language-settings-btn');
            langBtn.addEventListener('click', function () {
                alert('語言設定功能開發中...');
            });

            // 離開會議按鈕
            const leaveBtn = document.querySelector('.leave-meeting-btn');
            leaveBtn.addEventListener('click', function () {
                if (confirm('確定要離開會議嗎？')) {
                    alert('離開會議功能開發中...');
                }
            });

            // 語言標籤點擊效果
            const languageTags = document.querySelectorAll('.language-tag');
            languageTags.forEach(tag => {
                tag.addEventListener('click', function () {
                    this.classList.toggle('active');
                });
            });

            // 模擬即時翻譯更新
            const translationContents = document.querySelectorAll('.translation-content');
            const subtitleContent = document.querySelector('.subtitle-content p');

            // 每5秒更新一次內容（模擬即時翻譯）
            setInterval(() => {
                const now = new Date().toLocaleTimeString();
                if (subtitleContent) {
                    subtitleContent.textContent += ` [${now}] 新的即時字幕內容...`;
                    subtitleContent.scrollTop = subtitleContent.scrollHeight;
                }
            }, 5000);
        });
    </script>
</body>

</html>