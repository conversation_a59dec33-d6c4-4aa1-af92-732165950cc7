# QR Code Card 組件說明文件

## 概述

本文件說明新創建的 `qr-code-card` 組件，該組件已成功整合到 STT-SaaS 專案的 `translate.html` 頁面中。

## 組件特色

### ✅ 設計規範遵循
- **Material 3 Design 系統**：完全遵循 M3 設計規範
- **間距規則**：嚴格按照提供圖片中的藍色標記間距實現（1rem 和 2rem）
- **設計令牌**：使用專案現有的 `design-tokens/tokens.css` 中定義的顏色和字體變數

### ✅ 無障礙性 (WCAG 2.1)
- **語義化 HTML5**：使用 `<dialog>`、`<header>`、`<main>` 等語義標籤
- **ARIA 屬性**：完整的 `aria-label`、`aria-modal`、`aria-hidden` 支援
- **鍵盤導航**：支援 Tab 鍵導航和 ESC 鍵關閉
- **焦點管理**：開啟時聚焦關閉按鈕，關閉時返回觸發按鈕

### ✅ 響應式設計
- **桌面版**：400px 最大寬度，200px QR Code 尺寸
- **平板版**：適應中等螢幕尺寸
- **手機版**：100% 寬度，160px QR Code 尺寸，調整間距

## 檔案結構

### 修改的檔案
1. **`translate.html`**
   - 新增 QR Code 卡片 HTML 結構
   - 更新 JavaScript 互動邏輯（使用 `hidden` 類別）
   - 引入 `design-tokens/components.css`

2. **`design-tokens/components.css`**
   - 新增完整的 QR Code 卡片樣式
   - 包含響應式設計規則
   - 統一管理所有組件樣式

3. **`style.css`**
   - 移除 QR Code 卡片樣式（已移至 components.css）
   - 修正舊的 CSS 變數名稱
   - 保留頁面特定樣式

### 新增的檔案
1. **`qr-code-test.html`** - 獨立測試頁面

## 組件結構

```html
<div class="qr-code-card-overlay hidden" role="dialog" aria-modal="true">
    <div class="qr-code-card">
        <header class="qr-card-header">
            <h2 class="qr-card-title">會議連結</h2>
            <button class="qr-card-close-btn">×</button>
        </header>
        <div class="qr-card-content">
            <div class="qr-code-container">
                <div class="qr-code-image">
                    <!-- QR Code 內容 -->
                </div>
            </div>
            <div class="scan-button-container">
                <button class="scan-me-btn">
                    <span class="scan-icon">📱</span>
                    <span class="scan-text">SCAN ME</span>
                </button>
            </div>
            <div class="url-display-container">
                <div class="url-display">
                    <span class="url-text">https://www.fenrirdata.com</span>
                </div>
            </div>
        </div>
    </div>
</div>
```

## CSS 類別說明

### 主要容器
- `.qr-code-card-overlay` - 模態框遮罩層
- `.qr-code-card` - 卡片主體容器

### 標題區域
- `.qr-card-header` - 標題區域容器
- `.qr-card-title` - 卡片標題
- `.qr-card-close-btn` - 關閉按鈕

### 內容區域
- `.qr-card-content` - 內容主容器
- `.qr-code-container` - QR Code 容器
- `.qr-code-image` - QR Code 圖片區域
- `.scan-button-container` - SCAN ME 按鈕容器
- `.url-display-container` - 網址顯示容器

## 間距規則實現

根據提供的設計圖片標記：

### 1rem 間距應用
- 卡片外邊距：`padding: 1rem`
- 標題區域內邊距：`padding: 1rem 1rem 0 1rem`
- 標題下邊距：`margin-bottom: 1rem`
- 網址顯示內邊距：`padding: 1rem`

### 2rem 間距應用
- 內容區域內邊距：`padding: 0 2rem 2rem 2rem`
- 內容元素間距：`gap: 2rem`
- SCAN ME 按鈕內邊距：`padding: 1rem 2rem`

## 互動功能

### JavaScript 功能
1. **開啟卡片**：點擊 QR Code 浮動按鈕（移除 `hidden` 類別）
2. **關閉卡片**：
   - 點擊關閉按鈕 (×)
   - 點擊遮罩區域
   - 按下 ESC 鍵
   - 所有關閉方式都會添加 `hidden` 類別
3. **焦點管理**：自動處理焦點轉移
4. **滾動控制**：開啟時防止背景滾動

### 事件處理
```javascript
// 顯示卡片 - 使用 hidden 類別
qrBtn.addEventListener('click', function() {
    qrCardOverlay.classList.remove('hidden');
});

// 關閉卡片 - 使用 hidden 類別
function closeQrCard() {
    qrCardOverlay.classList.add('hidden');
}

// ESC 鍵檢查 - 使用 hidden 類別
if (e.key === 'Escape' && !qrCardOverlay.classList.contains('hidden')) {
    closeQrCard();
}
```

## 使用方式

### 基本使用
1. 確保引入必要的 CSS 檔案：
   ```html
   <link rel="stylesheet" href="design-tokens/components.css">
   <link rel="stylesheet" href="style.css">
   ```

2. 在 HTML 中添加 QR Code 卡片結構，並使用 `hidden` 類別：
   ```html
   <div class="qr-code-card-overlay hidden" id="qrCodeCardOverlay">
   ```

3. 引入 JavaScript 互動邏輯（使用 `classList` 方法）

### 自訂內容
- **QR Code 圖片**：替換 `.qr-code-placeholder` 內容
- **網址**：修改 `.url-text` 內的文字
- **標題**：修改 `.qr-card-title` 內容

## 技術規範

### 瀏覽器支援
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### 效能考量
- CSS 動畫使用 `transform` 和 `opacity` 以獲得最佳效能
- 使用 CSS 變數確保主題一致性
- 響應式圖片和字體大小

### 安全性
- 所有使用者輸入都經過適當的 HTML 轉義
- 遵循 CSP (Content Security Policy) 最佳實踐

## 測試

### 功能測試
1. 開啟 `qr-code-test.html` 進行獨立測試
2. 在 `translate.html` 中測試整合效果

### 無障礙測試
- 使用螢幕閱讀器測試
- 鍵盤導航測試
- 顏色對比度檢查

### 響應式測試
- 桌面：1920x1080
- 平板：768x1024
- 手機：375x667

## 未來擴展

### 可能的改進
1. **動態 QR Code 生成**：整合 QR Code 生成庫
2. **多語言支援**：國際化文字內容
3. **主題切換**：支援明暗主題
4. **動畫增強**：添加更豐富的過場動畫

### API 整合
```javascript
// 未來可整合的 QR Code 生成 API
function generateQRCode(url) {
    // QR Code 生成邏輯
}
```

## 重構優化 (2025-01-10)

### ✅ 樣式統一管理
- **集中化管理**：所有 QR Code 卡片樣式已移動到 `design-tokens/components.css`
- **一致性保證**：與其他組件使用相同的設計系統和管理方式
- **維護便利**：未來所有組件樣式都在 `components.css` 中統一管理

### ✅ 互動邏輯優化
- **使用 `hidden` 類別**：替代 `style.display` 的直接操作
- **更好的語義化**：`hidden` 類別更符合 HTML 語義
- **效能提升**：CSS 類別切換比直接樣式操作更高效

### ✅ 程式碼結構改善
```javascript
// 舊方式 - 直接操作 style
qrCardOverlay.style.display = 'flex';
qrCardOverlay.style.display = 'none';

// 新方式 - 使用 CSS 類別
qrCardOverlay.classList.remove('hidden');
qrCardOverlay.classList.add('hidden');
```

### ✅ 未來擴展性
- **統一的組件系統**：所有新組件都將遵循相同的管理模式
- **主題支援**：更容易實現明暗主題切換
- **樣式覆蓋**：更靈活的樣式自訂和覆蓋機制

## 結論

QR Code Card 組件已成功創建並整合到專案中，完全符合設計要求和技術規範。經過重構優化後，組件具備：

- ✅ 良好的無障礙性和響應式設計
- ✅ 統一的樣式管理系統
- ✅ 優化的互動邏輯
- ✅ 優秀的使用者體驗
- ✅ 高度的可維護性和擴展性

組件現在可以作為專案中的標準組件使用，並為未來的組件開發提供了良好的範例和基礎。
