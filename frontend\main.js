// 等待DOM完全載入後再執行腳本
document.addEventListener('DOMContentLoaded', () => {
    // 1. 選取所有需要的DOM元素
    const loginView = document.getElementById('login-view');
    const languageSelectView = document.getElementById('language-select-view');
    const loginForm = document.getElementById('login-form');
    const passwordInput = document.getElementById('host-name');
    // 2. 定義處理表單提交的函式
    const handleLoginSubmit = (event) => {
        // 防止表單提交時頁面重新整理
        event.preventDefault();
        const password = passwordInput.value;
        // --- 開始偵錯 ---
        console.log("------ 開始驗證 ------");
        console.log("從輸入框獲取的值:", `"${password}"`); // 用引號包起來，看清楚前後是否有空格
        console.log("值的型別是:", typeof password); // 你會發現它永遠是 string
        console.log("長度是否為6?", password.length === 6);
        console.log("是否為數字(isNaN 結果)?", isNaN(password)); // 直接看 isNaN 的結果
        console.log("!isNaN 的結果?", !isNaN(password));
        console.log("------ 結束驗證 ------");
        // --- 結束偵錯 ---
        // 原始驗證邏輯
        if (password.length === 6 && !isNaN(password)) {
            console.log('密碼驗證成功');
            loginView.classList.add('hidden');
            languageSelectView.classList.remove('hidden');
        } else {
            alert('請輸入6位數字的密碼！');
            passwordInput.value = "";
        }
    };
    // 3. 將提交事件綁定到登入表單
    // 我們監聽 `submit` 事件而非按鈕的 `click` 事件，
    // 這樣使用者透過鍵盤Enter鍵也能觸發，體驗更好。
    loginForm.addEventListener('submit', handleLoginSubmit);

    // 4. 語言選擇功能
    const languageSelect = document.getElementById('language-select');
    const selectedLanguagesContainer = document.getElementById('selected-languages');
    const confirmLanguageBtn = document.getElementById('confirm-language-btn');

    // 儲存已選擇的語言
    let selectedLanguages = [];

    // 語言選擇處理函式
    const handleLanguageSelect = (event) => {
        const selectedValue = event.target.value;
        const selectedText = event.target.options[event.target.selectedIndex].text;

        // 檢查是否已經選擇過這個語言
        if (selectedValue && !selectedLanguages.find(lang => lang.value === selectedValue)) {
            // 添加到已選擇語言陣列
            selectedLanguages.push({
                value: selectedValue,
                text: selectedText
            });

            // 更新顯示
            updateSelectedLanguagesDisplay();

            // 重置選擇框到預設選項
            event.target.selectedIndex = 0;
        }
    };

    // 更新已選擇語言的顯示
    const updateSelectedLanguagesDisplay = () => {
        selectedLanguagesContainer.innerHTML = '';

        selectedLanguages.forEach((language, index) => {
            const languagePill = document.createElement('span');
            languagePill.className = 'language-pill';
            languagePill.innerHTML = `
                ${language.text}
                <button class="remove-pill" data-index="${index}" type="button">&times;</button>
            `;

            selectedLanguagesContainer.appendChild(languagePill);
        });

        // 為移除按鈕添加事件監聽器
        const removeBtns = selectedLanguagesContainer.querySelectorAll('.remove-pill');
        removeBtns.forEach(btn => {
            btn.addEventListener('click', handleRemoveLanguage);
        });
    };

    // 移除語言處理函式
    const handleRemoveLanguage = (event) => {
        const index = parseInt(event.target.dataset.index);
        selectedLanguages.splice(index, 1);
        updateSelectedLanguagesDisplay();
    };

    // 確認語言選擇處理函式
    const handleConfirmLanguages = (event) => {
        event.preventDefault();

        if (selectedLanguages.length === 0) {
            alert('請至少選擇一種翻譯語言！');
            return;
        }

        console.log('已選擇的語言:', selectedLanguages);

        // 將語言選擇存儲到 sessionStorage，供下一頁使用
        sessionStorage.setItem('selectedLanguages', JSON.stringify(selectedLanguages));

        // 顯示確認訊息
        alert(`已選擇 ${selectedLanguages.length} 種語言：${selectedLanguages.map(lang => lang.text).join(', ')}`);

        // 跳轉到翻譯頁面
        window.location.href = 'translate.html';
    };

    // 綁定事件監聽器
    if (languageSelect) {
        languageSelect.addEventListener('change', handleLanguageSelect);
    }

    if (confirmLanguageBtn) {
        confirmLanguageBtn.addEventListener('click', handleConfirmLanguages);
    }
});