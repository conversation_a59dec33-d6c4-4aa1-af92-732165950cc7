<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Card 測試頁面</title>
    <!-- 引入設計令牌和組件樣式 -->
    <link rel="stylesheet" href="design-tokens/components.css">
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@100;300;400;500;600;700;800;900&display=swap">
    <!-- Material Symbols 圖示 -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
</head>
<body>
    <main style="min-height: 100vh; display: flex; align-items: center; justify-content: center; background-color: var(--md-sys-color-surface); padding: 2rem;">
        <div style="text-align: center;">
            <h1 style="color: var(--md-sys-color-on-surface); margin-bottom: 2rem;">QR Code Card 組件測試</h1>
            <p style="color: var(--md-sys-color-on-surface-variant); margin-bottom: 2rem;">點擊下方按鈕測試 QR Code 卡片組件</p>
            
            <!-- QR Code 浮動按鈕 -->
            <button class="qr-code-btn">
                <span class="material-symbols-outlined">qr_code_scanner</span>
            </button>
        </div>
        
        <!-- QR Code Card 模態框 -->
        <div class="qr-code-card-overlay hidden" id="qrCodeCardOverlay" role="dialog" aria-modal="true" aria-labelledby="qr-card-title">
            <div class="qr-code-card" role="document">
                <!-- 卡片標題區域 -->
                <header class="qr-card-header">
                    <h2 class="qr-card-title" id="qr-card-title">會議連結</h2>
                    <button class="qr-card-close-btn" aria-label="關閉會議連結視窗" type="button">
                        <span class="material-symbols-outlined" aria-hidden="true">close</span>
                    </button>
                </header>
                
                <!-- QR Code 內容區域 -->
                <main class="qr-card-content">
                    <!-- QR Code 圖片容器 -->
                    <div class="qr-code-container">
                        <div class="qr-code-image" role="img" aria-label="會議連結 QR Code">
                            <!-- QR Code 圖片將通過 CSS 背景圖片或 JavaScript 動態生成 -->
                            <div class="qr-code-placeholder">
                                <!-- 這裡可以放置實際的 QR Code 圖片或 SVG -->
                                <span class="material-symbols-outlined qr-placeholder-icon" aria-hidden="true">qr_code</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- SCAN ME 按鈕 -->
                    <div class="scan-button-container">
                        <button class="scan-me-btn" type="button" aria-label="掃描 QR Code 以加入會議">
                            <span class="material-symbols-outlined scan-icon" aria-hidden="true">smartphone</span>
                            <span class="scan-text">SCAN ME</span>
                        </button>
                    </div>
                    
                    <!-- 網址顯示區域 -->
                    <div class="url-display-container">
                        <div class="url-display" role="textbox" aria-readonly="true" aria-label="會議連結網址">
                            <span class="url-text">https://www.fenrirdata.com</span>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </main>

    <script>
        // QR Code 卡片互動功能
        document.addEventListener('DOMContentLoaded', function () {
            const qrBtn = document.querySelector('.qr-code-btn');
            const qrCardOverlay = document.getElementById('qrCodeCardOverlay');
            const qrCardCloseBtn = document.querySelector('.qr-card-close-btn');
            
            // 顯示 QR Code 卡片
            qrBtn.addEventListener('click', function () {
                qrCardOverlay.classList.remove('hidden');
                qrCardOverlay.setAttribute('aria-hidden', 'false');
                // 聚焦到關閉按鈕以提升無障礙體驗
                qrCardCloseBtn.focus();
                // 防止背景滾動
                document.body.style.overflow = 'hidden';
            });

            // 關閉 QR Code 卡片
            function closeQrCard() {
                qrCardOverlay.classList.add('hidden');
                qrCardOverlay.setAttribute('aria-hidden', 'true');
                // 恢復背景滾動
                document.body.style.overflow = '';
                // 將焦點返回到 QR Code 按鈕
                qrBtn.focus();
            }
            
            // 關閉按鈕點擊事件
            qrCardCloseBtn.addEventListener('click', closeQrCard);
            
            // 點擊遮罩關閉卡片
            qrCardOverlay.addEventListener('click', function (e) {
                if (e.target === qrCardOverlay) {
                    closeQrCard();
                }
            });
            
            // ESC 鍵關閉卡片
            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape' && !qrCardOverlay.classList.contains('hidden')) {
                    closeQrCard();
                }
            });
        });
    </script>
</body>
</html>
